<div>
  <div
    class="pay-wrap dialog-animation z-100 absolute left-0 top-0 flex h-full w-full justify-center"
    v-show="cz_qudan"
    :style="{ opacity: 0 }"
    ref="payWrap"
    @error="handleError"
  >
    <div
      class="pay-wrap-inner flex space-x-4"
      :style="{ transform: `translateX(${payWrapInnerLeft}px)`}"
    >
      <div
        class="f-pay-vl-box-item-animation rounded-100 center active:mt-8.5 bg-white mt-8 h-10 w-10 cursor-pointer text-xl shadow-lg transition hover:opacity-70 hover:shadow-base"
        style="animation-delay: 100ms; opacity: 0"
        @click="cz_qudan_close"
      >
        <i class="el-icon-arrow-left"></i>
      </div>

      <div
        class="f-pay-vl-box f-pay-vl-box-item-animation hover:shadow-baseh bg-white mt-2 flex shrink-0 flex-col justify-between overflow-hidden rounded-lg shadow-base transition"
        style="animation-delay: 200ms; opacity: 0"
      >
        <div
          class="f-pay-scroll-box scrollbar-hide relative overflow-y-auto"
          ref="scrollBox"
        >
          <div
            class="f-pay-title-1 bg-white group top-0 z-10 flex shrink-0 cursor-pointer items-center justify-between px-6 text-lg font-bold"
            @click="handleScrollToTitle_1"
          >
            <div class="o-font-shadow">消费明细</div>
            <ficon
              icon="MaterialSymbolsExpandAll"
              class="text-gray text-sm opacity-0 transition-all group-hover:opacity-100 group-active:translate-y-0.5"
            ></ficon>
          </div>
          <div
            id="f-pay-inner-box"
            class="scrollbar-hide box-border w-full overflow-y-auto px-8 py-6 text-sm transition-all"
            ref="innerBox"
          >
            <el-skeleton
              class="w-full space-y-4"
              :loading="endPay!=1 && orderListLoading"
              animated
            >
              <template slot="template">
                <div class="flex space-x-2" v-for="i in 2" :key="i">
                  <el-skeleton-item variant="text" style="width: 20px" />
                  <div class="flex-1 space-y-3 pb-3">
                    <div class="space-y-1">
                      <el-skeleton-item variant="text" />
                      <el-skeleton-item variant="text" />
                      <el-skeleton-item variant="text" />
                    </div>
                    <el-divider></el-divider>
                  </div>
                </div>
              </template>
              <template>
                <div
                  class="item-animation flex w-full space-x-1"
                  v-for="(item,index) in orderDetails.orderInfo"
                  :style="{ animationDelay: index * 100 + 'ms' }"
                  :key="'orderInfo'+index"
                >
                  <div
                    class="w-5 shrink-0 font-bold text-primary"
                    style="margin-top: -2px"
                  >
                    {{index+1}}.
                  </div>
                  <div class="flex-1 space-y-2 leading-4">
                    <div class="flex justify-between space-x-4">
                      <div>{{item.name}}</div>
                      <div class="shrink-0">￥{{item.price}}</div>
                    </div>
                    <div class="flex justify-between space-x-4">
                      <div>数量</div>
                      <div class="shrink-0">×{{item.num}}</div>
                    </div>
                    <!-- <div class="order_info_li" v-if="item.reduceprice">
                      <span v-if="item.equity_type==2">折扣</span>
                      <span v-if="item.equity_type==3">抵扣</span>
                      <span v-if="item.equity_type==4">优惠金额</span>
                      <span>-￥{{item.reduceprice | filterMoney}}</span>
                  </div> -->
                    <div
                      class="flex justify-between space-x-4"
                      v-if="item.equity_type!=1"
                    >
                      <div v-if="item.equity_type==3">次卡抵扣</div>
                      <div v-if="item.equity_type==2">充值卡折扣</div>
                      <div class="shrink-0" v-if="item.equity_type!=4">
                        -￥{{item.reduceprice | formatMark}}
                      </div>
                    </div>
                    <div
                      class="flex justify-between space-x-4"
                      v-if="item.equity_type==4"
                    >
                      <div v-if="item.equity_type==4">优惠金额</div>
                      <div
                        v-if="item.reduceprice && item.reduceprice.indexOf('-')==-1"
                      >
                        +￥{{item.reduceprice | formatMark}}
                      </div>
                      <div
                        class="shrink-0"
                        v-if="item.reduceprice && item.reduceprice.indexOf('-')!=-1"
                      >
                        -￥{{item.reduceprice | formatMark}}
                      </div>
                    </div>
                    <div
                      class="flex justify-between space-x-4"
                      style="margin-bottom: 1rem"
                    >
                      <div>小计</div>
                      <div class="shrink-0">￥{{item.Subtotal}}</div>
                    </div>
                    <el-divider></el-divider>
                  </div>
                </div>
              </template>
            </el-skeleton>
          </div>

          <template
            v-if="orderDetails.presentData && orderDetails.presentData.length>0"
          >
            <div
              class="f-pay-title-2 bg-white group z-10 flex shrink-0 cursor-pointer items-center justify-between px-6 text-lg font-bold"
              @click="handleScrollToTitle_2"
              ref="title2Element"
            >
              <div class="o-font-shadow">赠送明细</div>
              <ficon
                icon="MaterialSymbolsExpandAll"
                class="text-gray text-sm opacity-0 transition-all group-hover:opacity-100 group-active:translate-y-0.5"
              ></ficon>
            </div>
            <div class="flex flex-col">
              <div
                class="f-pay-inner-box scrollbar-hide box-border w-full overflow-y-auto px-8 py-6 text-sm transition-all"
              >
                <el-skeleton
                  class="w-full space-y-4"
                  :loading="endPay!=1 && orderListLoading"
                  animated
                >
                  <template slot="template">
                    <div class="flex space-x-2">
                      <el-skeleton-item variant="text" style="width: 20px" />
                      <div class="flex-1 space-y-1">
                        <el-skeleton-item variant="text" />
                        <el-skeleton-item variant="text" />
                        <el-skeleton-item variant="text" />
                      </div>
                    </div>
                  </template>
                  <template>
                    <div
                      class="item-animation flex w-full space-x-1"
                      v-for="(item,index) in orderDetails.presentData"
                      :style="{ animationDelay: index * 100 + 'ms' }"
                      :key="'presentData'+index"
                    >
                      <div
                        class="w-5 shrink-0 font-bold text-primary"
                        style="margin-top: -2px"
                      >
                        {{index+1}}.
                      </div>
                      <div class="flex-1 space-y-2 leading-4">
                        <div class="flex justify-between space-x-4">
                          <div>
                            <!-- <span>{{item.name}}</span>
                    <span v-if="item.sku_name">| {{item.sku_name}}</span> -->
                            <div v-if="item.itemType==1">
                              {{item.name}}（服务）
                            </div>
                            <div v-if="item.itemType==2 && !item.sku_name">
                              {{item.name}}（产品）
                            </div>
                            <div v-if="item.itemType==2 && item.sku_name">
                              {{item.name}}
                            </div>
                            <div v-if="item.sku_name">
                              | {{item.sku_name}}（产品）
                            </div>
                          </div>
                          <div class="shrink-0">
                            ￥{{filterMoney(item.price)}}
                          </div>
                        </div>
                        <div class="flex justify-between space-x-4">
                          <div>数量</div>
                          <div class="shrink-0">×{{item.num}}</div>
                        </div>
                        <div
                          class="flex justify-between space-x-4"
                          style="margin-bottom: 1rem"
                        >
                          <div>赠送状态</div>
                          <div class="shrink-0">
                            <span class="order-label2" v-if="item.status==0"
                              >仅选择</span
                            >
                            <span class="order-label2" v-if="item.status==1"
                              >已赠送</span
                            >
                            <span class="order-label2" v-if="item.status==2"
                              >已退回</span
                            >
                          </div>
                        </div>
                        <el-divider></el-divider>
                      </div>
                    </div>
                  </template>
                </el-skeleton>
              </div>
            </div>
          </template>
        </div>

        <div class="f-pay-money-box shrink-0 space-y-3 px-6 pb-4 pt-6">
          <el-skeleton
            class="w-full space-y-1 text-sm"
            :loading="endPay!=1 && orderListLoading"
            animated
          >
            <template slot="template">
              <el-skeleton-item variant="text" />
            </template>
            <template>
              <div class="flex justify-between space-x-4">
                <div>合计</div>
                <div class="shrink-0">￥{{filterMoney(receivableing)}}</div>
              </div>
              <div
                class="flex justify-between space-x-4"
                v-if="kd_xinxi_list?.member_counpon_money!=undefined && kd_xinxi_list?.member_counpon_money!=0 && kd_xinxi_list?.member_coupon!=0"
              >
                <div>优惠</div>
                <div class="shrink-0">
                  -￥{{filterMoney(kd_xinxi_list.member_counpon_money)}}
                </div>
              </div>
              <div
                class="flex justify-between space-x-4"
                v-if="kd_xinxi_list.dismoney"
              >
                <div>充值卡折扣</div>
                <div class="shrink-0" v-if="kd_xinxi_list.dismoney">
                  -￥{{filterMoney(kd_xinxi_list.dismoney)}}
                </div>
              </div>
              <div
                class="flex justify-between space-x-4"
                v-if="kd_xinxi_list.deduction"
              >
                <div>次卡抵扣</div>
                <div class="shrink-0" v-if="kd_xinxi_list.deduction">
                  -￥{{filterMoney(kd_xinxi_list.deduction)}}
                </div>
              </div>
              <div
                class="flex justify-between space-x-4"
                v-if="kd_xinxi_list.manually"
              >
                <div>优惠金额</div>
                <div class="shrink-0" v-if="kd_xinxi_list.manually>0">
                  -￥{{orderDetails.manuallys}}
                </div>
                <div class="shrink-0" v-else>+￥{{orderDetails.manuallys}}</div>
              </div>
              <div
                class="flex justify-between space-x-4"
                v-if="orderDetails.small_change_money"
              >
                <div>抹零</div>
                <div class="shrink-0" v-if="orderDetails.small_change_money">
                  -￥{{filterMoney(orderDetails.small_change_money)}}
                </div>
              </div>
              <div
                class="flex justify-between space-x-4"
                v-if="kd_xinxi_list.net_receipts>0"
              >
                <div>预约定金</div>
                <div class="shrink-0" v-if="kd_xinxi_list.net_receipts">
                  ￥{{filterMoney(kd_xinxi_list.net_receipts)}}
                </div>
              </div>
              <!-- <div class="order_info_li" v-if="isDebtFlag">
                  <span>合计</span>
                  <span>￥{{orderDetails.receivable}}</span>
              </div> -->
              <div class="flex justify-between space-x-4" v-if="isDebtFlag">
                <div>已付款</div>
                <div class="shrink-0">
                  -￥{{filterMoney(orderDetails.alreadyPay)}}
                </div>
              </div>
              <div class="flex justify-between space-x-4" v-if="isDebtMoney">
                <div>欠款</div>
                <div class="shrink-0">
                  ￥{{filterMoney(debtForm.debtMoney)}}
                </div>
              </div>
            </template>
          </el-skeleton>
          <el-divider></el-divider>
          <el-skeleton
            class="flex w-full items-center justify-between font-bold"
            :loading="endPay!=1 && getOrderDetailsLoading"
            animated
          >
            <template slot="template">
              <el-skeleton-item variant="h3" />
            </template>
            <template>
              <div class="o-font-shadow text-lg">
                <span v-show="endPay==0">待支付</span>
                <span v-show="endPay==1">已支付</span>
              </div>
              <div
                class="o-font-shadow flex items-baseline"
                v-if="!isDebtMoney"
              >
                <div class="pr-1">￥</div>
                <div class="text-2xl">
                  {{filterMoney(kd_xinxi_list.toBePay).split('.')[0]}}
                </div>
                <div class="text-lg">
                  .{{filterMoney(kd_xinxi_list.toBePay).split('.')[1]}}
                </div>
              </div>
              <div class="o-font-shadow flex items-baseline" v-if="isDebtMoney">
                <div class="pr-1">￥</div>
                <div class="text-2xl">
                  {{filterMoney(debtForm.payMoney || 0.00).split('.')[0]}}
                </div>
                <div class="text-lg">
                  .{{filterMoney(debtForm.payMoney || 0.00).split('.')[1]}}
                </div>
              </div>
            </template>
          </el-skeleton>
        </div>
      </div>

      <div
        style="animation-delay: 300ms; opacity: 0"
        class="f-pay-vl-box f-pay-vl-box-item-animation mt-2 flex flex-col space-y-4 transition-all"
      >
        <div
          class="hover:shadow-baseh bg-white flex shrink-0 flex-col overflow-hidden rounded-lg pb-4 shadow-base transition"
        >
          <div
            class="f-pay-title-1 o-font-shadow bg-white group top-0 z-10 flex shrink-0 items-center px-6 text-lg font-bold"
          >
            订单信息
          </div>

          <el-skeleton
            class="relative z-10 space-y-2 px-6 pb-4 pt-5 text-sm"
            :loading="endPay!=1 && orderListLoading"
            animated
          >
            <template slot="template">
              <div class="space-y-2">
                <el-skeleton-item v-for="i in 4" :key="'l2'+i" variant="text" />
              </div>
            </template>
            <template>
              <div class="item-animation flex items-center justify-between">
                <div class="shrink-0">订单编号</div>
                <div>{{kd_xinxi_list.order_number}}</div>
              </div>
              <div
                class="item-animation flex items-center justify-between"
                style="height: 33px"
                v-if="endPay==1"
              >
                <div class="shrink-0">下单时间</div>
                <div>
                  {{manualOrderTime?manualOrderTime:kd_xinxi_list.order_time}}
                </div>
              </div>
              <div
                class="item-animation flex items-center justify-between"
                style="height: 33px; animation-delay: 100ms"
                v-else
                @click="handleModifyOrderTime"
              >
                <div
                  class="flex shrink-0 cursor-pointer items-center space-x-2 text-primary hover:text-primary/70"
                >
                  <div>下单时间</div>
                  <i class="el-icon-edit"></i>
                </div>
                <div v-if="!manualOrderTime && !isEditOrderTime">
                  {{kd_xinxi_list.order_time}}
                </div>
                <el-date-picker
                  size="small"
                  v-if="isEditOrderTime"
                  v-model="manualOrderTime"
                  type="datetime"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  placeholder="选择日期时间"
                  style="width: 200px"
                ></el-date-picker>
              </div>
              <!-- <div
                class="item-animation flex items-center justify-between"
                v-if="kd_xinxi_list_buyer && kd_xinxi_list_buyer.id!=0"
                style="animation-delay: 250ms"
              >
                <div class="shrink-0">会员编号</div>
                <div>{{kd_xinxi_list?.buyer?.member_number}}</div>
              </div> -->
              <div
                class="item-animation flex items-center justify-between"
                v-if="kd_xinxi_list_buyer && kd_xinxi_list_buyer.id!=0"
                style="animation-delay: 300ms"
              >
                <div class="shrink-0">客户</div>
                <div v-if="kd_xinxi_list_buyer && kd_xinxi_list_buyer.id!=0">
                  <span>{{kd_xinxi_list_buyer.member_name}}</span>
                  <el-tag size="mini" type="warning"> 会员 </el-tag>
                </div>
                <div v-else>普通顾客</div>
              </div>
              <!-- <div
                class="item-animation flex items-center justify-between"
                style="animation-delay: 350ms"
              >
                <div class="shrink-0">收银员</div>
                <div>{{kd_xinxi_list_cashierInfo.nickname}}</div>
              </div> -->
            </template>
          </el-skeleton>
        </div>
        <div
          class="hover:shadow-baseh bg-white relative flex flex-1 flex-col justify-between overflow-hidden rounded-lg shadow-base transition"
        >
          <div class="center h-full w-full flex-col" v-if="endPay==1">
            <div class="f-success-box">
              <div class="tick_mark"></div>
            </div>
            <div class="mb-10 mt-5 font-bold">支付成功</div>
            <div class="box-border flex w-full flex-col space-y-5 px-6">
              <f-base-button
                class=""
                title="打印小票"
                type="light"
                @click="bindPrint"
              >
                <ficon
                  icon="RiPrinterLine"
                  class="text-gray-500 pr-2 text-[17px]"
                />
              </f-base-button>
              <f-base-button class="" title="返回" @click="cz_qudan_close">
                <ficon
                  icon="RiArrowLeftSLine"
                  class="text-gray-500 pr-2 text-lg"
                />
              </f-base-button>
            </div>
          </div>
          <template v-else>
            <div
              class="f-pay-title-1 o-font-shadow bg-white group top-0 z-10 flex shrink-0 items-center px-6 text-lg font-bold"
            >
              收银方式
            </div>
            <div
              v-if="!orderListLoading"
              class="scrollbar-hide relative z-10 h-0 flex-1 space-y-4 overflow-y-auto px-3 py-4"
            >
              <el-checkbox-group v-model="payStyleCheckList">
                <div
                  v-if="isConsumeCards"
                  class="border-gray-200 cursor-pointer rounded-md border border-solid px-4 py-5 transition hover:border-primary"
                  :class="payStyleCheckList.includes('卡项抵扣') ? 'bg-primary/10': ''"
                  @click="handlePayStyleCheckClick('卡项抵扣')"
                >
                  <el-checkbox
                    label="卡项抵扣"
                    style="pointer-events: none"
                  ></el-checkbox>
                </div>
                <template v-else>
                  <div
                    v-if="!isRecharge && kd_xinxi_list_buyer?.id!=0 && payCardInfo.length>0 && payCardInfo[0]?.residuebalance > 0"
                    class="border-gray-200 mb-2 cursor-pointer rounded-md border border-solid px-4 py-5 transition hover:border-primary"
                    :class="payStyleCheckList.includes('会员余额') ? 'bg-primary/10': ''"
                    @click="handlePayStyleCheckClick('会员余额')"
                  >
                    <el-checkbox
                      :label="`会员余额：￥${filterMoney(payCardInfo[0].residuebalance)}`"
                      name="会员余额"
                      style="pointer-events: none"
                    ></el-checkbox>
                  </div>
                  <div class="grid grid-cols-2 gap-2">
                    <div
                      v-for="(store,index) of customizePayTypeList"
                      :key="'pbt'+index"
                      class="border-gray-200 cursor-pointer rounded-md border border-solid px-4 py-5 transition hover:border-primary"
                      :class="payStyleCheckList.includes(store.name) ? 'bg-primary/10': ''"
                      @click="handlePayStyleCheckClick(store.name,store.payType)"
                    >
                      <el-checkbox
                        :label="store.name"
                        style="pointer-events: none"
                      ></el-checkbox>
                    </div>
                    <div
                      class="border-gray-200 cursor-pointer rounded-md border border-solid px-4 py-5 transition hover:border-primary"
                      :class="payStyleCheckList.includes('现金') ? 'bg-primary/10': ''"
                      @click="handlePayStyleCheckClick('现金')"
                    >
                      <el-checkbox
                        label="现金"
                        style="pointer-events: none"
                      ></el-checkbox>
                    </div>
                  </div>
                </template>
              </el-checkbox-group>
              <!-- <f-pay-btn
                v-if="isConsumeCards"
                title="卡项抵扣"
                @click="handlePayBtnClick_2('卡项抵扣')"
                class="item-animation"
                style="animation-delay: 100ms"
              />
              <template v-else>
                <f-pay-btn
                  v-if="!isRecharge && kd_xinxi_list_buyer?.id!=0 && payCardInfo.length>0 && payCardInfo[0]?.residuebalance > 0"
                  :is-laka-selected="isLakalaSelected"
                  :insufficient-balance="payCardInfo[0].residuebalance<kd_xinxi_list.toBePay"
                  :title="`会员余额：￥${filterMoney(payCardInfo[0].residuebalance)}`"
                  @click="handlePayBtnClick_2('会员余额')"
                  class="item-animation"
                  style="animation-delay: 100ms"
                />
                <f-pay-btn
                  v-for="(store,index) of customizePayTypeList"
                  :is-laka-selected="isLakalaSelected"
                  :key="'pbt'+index"
                  :title="store.name"
                  :is-laka="store.payType == 11"
                  @click="handlePayBtnClick_2('自定义记账',store.payType)"
                  class="item-animation"
                  :style="{ animationDelay: (index + 2) * 100 + 'ms' }"
                />
                <f-pay-btn
                  :is-laka-selected="isLakalaSelected"
                  title="现金"
                  @click="handlePayBtnClick_2('现金')"
                  class="item-animation"
                  :style="{ animationDelay: (customizePayTypeList.length + 2) * 100 + 'ms' }"
                />
              </template> -->
            </div>
            <div
              class="f-pay-paying-mask center absolute left-0 top-0 z-30 h-full w-full"
              :style="{ opacity: 0 }"
              v-if="isPaying"
            >
              <div class="f-pay-paying-text o-font-shadow text-primary">
                支付中...
              </div>
            </div>
            <div class="f-pay-foot-shadow shrink-0 px-2 py-4">
              <f-base-button
                class=""
                title="收款"
                type="primary"
                :disabled="isDisablePayBtn"
                @click="handlePayBtnClick"
              />
            </div>
          </template>
        </div>
      </div>
      <div
        v-if="isShowAllocateDialog"
        class="f-pay-vl-box f-pay-vl-box-item-animation-lakala hover:shadow-baseh bg-white mt-2 flex shrink-0 flex-col justify-between overflow-hidden rounded-lg shadow-base transition"
        :class="{ 'f-pay-lakala-out': !isLakalaSelected }"
      >
        <div
          class="f-pay-title-3 o-font-shadow bg-white top-0 z-10 shrink-0 px-6 pb-4"
        >
          ewqre
        </div>
      </div>
      <!-- <div
        v-if="isShowLakalaDialog"
        class="f-pay-vl-box f-pay-vl-box-item-animation-lakala hover:shadow-baseh bg-white mt-2 flex shrink-0 flex-col justify-between overflow-hidden rounded-lg shadow-base transition"
        :class="{ 'f-pay-lakala-out': !isLakalaSelected }"
      >
        <div
          class="f-pay-title-3 o-font-shadow bg-white top-0 z-10 shrink-0 px-6 pb-4"
        >
          <div
            class="f-pay-title-text flex cursor-pointer items-center justify-between"
          >
            <div class="flex-1 text-lg font-bold">关联外部收入账单</div>
            <div
              class="text-gray-400 shrink-0 pr-2 text-sm transition hover:text-primary"
              :class="{'line-through':!isLimitPrice}"
              @click="limitPrice"
            >
              &nbsp;限价&nbsp;
            </div>
            <i
              @click="handleRefreshAmountList"
              :class="{'animate-spin':paymentLoading}"
              class="el-icon-refresh text-gray shrink-0 transition hover:text-primary active:translate-y-0.5"
            ></i>
          </div>
          <el-date-picker
            style="width: 300px"
            v-model="custmizePaySearchForm.date"
            type="daterange"
            align="right"
            unlink-panels
            size="small"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            :picker-options="pickerOptions"
            @change="handleAmountChange"
          ></el-date-picker>
        </div>

        <div class="o-scrollbar scrollbar-hide h-0 flex-1 overflow-y-auto px-2">
          <div
            v-if="paymentList.length === 0"
            class="center h-full w-full flex-col"
          >
            <div
              class="hover: group cursor-pointer active:translate-y-0.5"
              @click="handleRefreshAmountList"
            >
              <img
                class="select-none opacity-70 transition group-hover:opacity-100"
                src="images/icon/Empty.svg"
                style="width: 119px; height: 98px"
              />
              <div
                class="text-gray-400 mt-4 text-sm transition-all group-hover:drop-shadow-md"
              >
                暂无数据，请<span
                  class="text-gray transition group-hover:text-primary"
                  >刷新</span
                >
              </div>
            </div>
          </div>
          <div
            v-for="item in paymentList"
            class="f-pay-paylist-item-box group cursor-pointer"
            :class="linkPaymentId==item.logNo?'is-active sticky top-0 bottom-0':''"
            :key="item.logNo"
            @click="handlePaymentClick(item.logNo)"
          >
            <div class="f-pay-paylist-item-box-inner px-4">
              <template v-if="linkPaymentId==item.logNo">
                <ficon
                  icon="MaterialSymbolsLink"
                  class="w-9 shrink-0 text-lg text-primary transition group-hover:hidden"
                ></ficon>
                <ficon
                  icon="MaterialSymbolsLinkOff"
                  class="hidden w-9 shrink-0 text-lg text-primary transition group-hover:block"
                ></ficon>
              </template>
              <ficon
                v-else
                icon="MaterialSymbolsLink"
                class="text-gray-300 group-hover:text-gray-400 w-9 shrink-0 text-lg transition"
              ></ficon>
              <div class="flex-1">
                <div class="text-gray text-xs">{{item.logNo}}</div>
                <div class="text-sm">{{item.tradeTime}}</div>
              </div>
              <div class="flex shrink-0 items-baseline font-bold">
                <span class="text-sm">￥</span>
                <span class="text-lg">{{(item.amount).split('.')[0]}}</span>
                <span class="text-sm">.{{(item.amount).split('.')[1]}}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="f-pay-foot-shadow flex justify-end pb-4 pt-3">
          <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :page-sizes="[20,50, 100,]"
            :page-size="limit"
            :current-page.sync="currentPage"
            layout="prev, pager, next"
            :total="allCount"
          ></el-pagination>
        </div>
      </div> -->
    </div>
  </div>

  <!--默认打印-->
  <app-print
    :store="userInfo"
    :order-info="kd_xinxi_list"
    :member="kd_xinxi_list_buyer"
    :pay-type="isPay"
    :pay-money="cz_shou_qian"
    :change-money="kd_shishou"
  ></app-print>
  <!--打印样式-->
  <div
    class="printWrap"
    :style="{width:paperwidth +'px'}"
    ref="printorderstr"
    style="display: none; top: 150px"
  >
    <template v-for="(item,index) in printSet.set">
      <template v-if="item.name=='store'">
        <app-store :store-set="item.set" :store="userInfo"></app-store>
      </template>
      <template v-if="item.name=='header'">
        <app-header
          :header-set="item.set"
          :order-header="kd_xinxi_list"
        ></app-header>
      </template>
      <template v-if="item.name=='goods'">
        <app-goods
          :goods-set="item.set"
          :goods="orderDetails.orderInfo"
          direct="0"
        ></app-goods>
      </template>
      <template v-if="item.name=='vip'">
        <app-vip
          :vip-set="item.set"
          :member="kd_xinxi_list_buyer"
          :order-details="orderDetails"
          :print-type="2"
        ></app-vip>
      </template>
      <!--<template v-if="item.name=='takegoods'">-->
      <!--<app-address :address-set="item.set"></app-address>-->
      <!--</template>-->
      <template v-if="item.name=='footer'">
        <app-footer
          :footer-set="item.set"
          :order-footer="orderDetails"
          :pay-type="isPay"
          :pay-money="cz_shou_qian"
          :change-money="kd_shishou"
        ></app-footer>
      </template>
      <template v-if="item.name=='line'">
        <app-line :line-set="item.set"></app-line>
      </template>
      <template v-if="item.name=='info'">
        <app-info :info-set="item.set"></app-info>
      </template>
      <template v-if="item.name=='text'">
        <app-text :text-set="item.set"></app-text>
      </template>
    </template>
  </div>
</div>
